import { ChevronRight } from "lucide-react";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardAction, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const patients = [
  {
    id: 1,
    name: "<PERSON>",
    avatar: `${process.env.ASSETS_URL}/avatars/01.png`,
    email: "<EMAIL>",
    lastProcedure: "Appendectomy",
    date: "2025-05-20"
  },
  {
    id: 2,
    name: "<PERSON>",
    avatar: `${process.env.ASSETS_URL}/avatars/02.png`,
    email: "<EMAIL>",
    lastProcedure: "Knee Arthroscopy",
    date: "2025-05-18"
  },
  {
    id: 3,
    name: "<PERSON>",
    avatar: `${process.env.ASSETS_URL}/avatars/03.png`,
    email: "<EMAIL>",
    lastProcedure: "Cataract Surgery",
    date: "2025-05-15"
  },
  {
    id: 4,
    name: "<PERSON>",
    avatar: `${process.env.ASSETS_URL}/avatars/04.png`,
    email: "<EMAIL>",
    lastProcedure: "Colonoscopy",
    date: "2025-05-12"
  },
  {
    id: 5,
    name: "Can Jackson",
    avatar: `${process.env.ASSETS_URL}/avatars/05.png`,
    email: "<EMAIL>",
    lastProcedure: "Colonoscopy",
    date: "2025-08-12"
  }
];

export function PatientsWithLastProcedure() {
  return (
    <Card className="col-span-3">
      <CardHeader className="relative">
        <CardTitle>Patients with Last Procedure</CardTitle>
        <CardAction>
          <Button variant="outline">
            View All <ChevronRight />
          </Button>
        </CardAction>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="space-y-6">
          {patients.map((patient) => (
            <div key={patient.id} className="flex items-center">
              <Avatar className="size-10">
                <AvatarImage src={patient.avatar} alt="Avatar" />
                <AvatarFallback>
                  {patient.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div className="ml-3 space-y-1">
                <p className="leading-none font-medium">{patient.name}</p>
                <p className="text-muted-foreground text-sm">{patient.email}</p>
              </div>
              <div className="ml-auto space-y-1 text-end font-medium">
                <p className="text-sm">{patient.lastProcedure}</p>
                <p className="text-muted-foreground text-sm">{patient.date}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
