import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Mail, MapPin, Phone } from "lucide-react";
import Link from "next/link";

export default function SplitWithDetails() {
  return (
    <div className="container mx-auto px-4 py-24 md:px-6 lg:py-32 2xl:max-w-[1400px]">
      <div className="mx-auto max-w-2xl lg:max-w-5xl">
        <div className="text-center">
          <h1 className="text-3xl font-bold sm:text-4xl">Contact us</h1>
          <p className="text-muted-foreground mt-3">
            We&apos;d love to talk about how we can help you.
          </p>
        </div>

        <div className="mt-12 grid items-center gap-6 lg:grid-cols-2 lg:gap-16">
          <Card className="p-0">
            <CardContent className="p-6">
              <h2 className="mb-8 text-xl font-semibold">Fill in the form</h2>
              <form>
                <div className="grid gap-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <Input type="text" placeholder="First Name" />
                    </div>
                    <div>
                      <Input type="text" placeholder="Last Name" />
                    </div>
                  </div>

                  <div>
                    <Input type="email" placeholder="Email" />
                  </div>

                  <div>
                    <Input type="tel" placeholder="Phone Number" />
                  </div>

                  <div>
                    <Textarea placeholder="Details" rows={4} />
                  </div>
                </div>

                <div className="mt-4 grid">
                  <Button type="submit">Send inquiry</Button>
                </div>

                <div className="mt-3 text-center">
                  <p className="text-muted-foreground text-sm">
                    We&apos;ll get back to you in 1-2 business days.
                  </p>
                </div>
              </form>
            </CardContent>
          </Card>

          <div className="divide-border divide-y">
            <div className="flex gap-x-7 py-6">
              <MapPin className="text-muted-foreground mt-1.5 size-6" />
              <div>
                <h3 className="font-semibold">Our address</h3>
                <p className="text-muted-foreground mt-1 text-sm">
                  We&apos;re here to help with any questions or code.
                </p>
                <address className="text-muted-foreground mt-2 text-sm not-italic">
                  300 Bath Street, Tay House
                  <br />
                  Glasgow G2 4JR, United Kingdom
                </address>
              </div>
            </div>

            <div className="flex gap-x-7 py-6">
              <Mail className="text-muted-foreground mt-1.5 size-6" />
              <div>
                <h3 className="font-semibold">Email us</h3>
                <p className="text-muted-foreground mt-1 text-sm">
                  We&apos;ll get back to you as soon as possible.
                </p>
                <p className="mt-2">
                  <Link
                    className="text-primary text-sm font-medium hover:underline"
                    href="mailto:<EMAIL>"
                  >
                    <EMAIL>
                  </Link>
                </p>
              </div>
            </div>

            <div className="flex gap-x-7 py-6">
              <Phone className="text-muted-foreground mt-1.5 size-6" />
              <div>
                <h3 className="font-semibold">Call us</h3>
                <p className="text-muted-foreground mt-1 text-sm">
                  Mon-Fri from 8am to 5pm.
                </p>
                <p className="mt-2">
                  <Link
                    className="text-primary text-sm font-medium hover:underline"
                    href="tel:+****************"
                  >
                    +****************
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
