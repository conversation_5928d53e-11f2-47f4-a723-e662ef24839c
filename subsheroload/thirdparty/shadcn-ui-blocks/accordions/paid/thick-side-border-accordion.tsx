import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export default function SideBorderAccordion() {
  return (
    <div className="max-w-md mx-auto py-6">
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem
          value="item-1"
          className="border-b border-l-0 data-[state=open]:border-l-4 data-[state=open]:border-l-foreground/30 pl-4 transition-all duration-300"
        >
          <AccordionTrigger className="hover:no-underline data-[state=open]:font-medium">
            Is it accessible?
          </AccordionTrigger>
          <AccordionContent>
            Yes. It adheres to the WAI-ARIA design pattern.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem
          value="item-2"
          className="border-b border-l-0 data-[state=open]:border-l-4 data-[state=open]:border-l-foreground/30 pl-4 transition-all duration-300"
        >
          <AccordionTrigger className="hover:no-underline data-[state=open]:font-medium">
            Is it styled?
          </AccordionTrigger>
          <AccordionContent>
            Yes. It comes with default styles that matches the other
            components&apos; aesthetic.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem
          value="item-3"
          className="border-b border-l-0 data-[state=open]:border-l-4 data-[state=open]:border-l-foreground/30 pl-4 transition-all duration-300"
        >
          <AccordionTrigger className="hover:no-underline data-[state=open]:font-medium">
            Is it animated?
          </AccordionTrigger>
          <AccordionContent>
            Yes. It&apos;s animated by default, but you can disable it if you
            prefer.
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
