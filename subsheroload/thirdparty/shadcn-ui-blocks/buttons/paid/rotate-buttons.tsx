import { Button } from "@/components/ui/button";

export default function RotateButtons() {
  return (
    <div className="flex flex-wrap gap-4 p-6">
      <Button
        variant="default"
        className="hover:rotate-3 transition-transform duration-300"
      >
        Default
      </Button>

      <Button
        variant="secondary"
        className="hover:rotate-3 transition-transform duration-300"
      >
        Secondary
      </Button>

      <Button
        variant="destructive"
        className="hover:rotate-3 transition-transform duration-300"
      >
        Destructive
      </Button>

      <Button
        variant="outline"
        className="hover:rotate-3 transition-transform duration-300"
      >
        Outline
      </Button>

      <Button
        variant="ghost"
        className="hover:rotate-3 transition-transform duration-300"
      >
        Ghost
      </Button>
    </div>
  );
}
