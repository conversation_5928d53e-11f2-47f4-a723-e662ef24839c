import { Twitter, Gith<PERSON>, <PERSON>ed<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Image from "next/image";

const team = [
  {
    name: "<PERSON>",
    role: "Founder / CEO",
    bio: "<PERSON> has been in the tech industry for over 12 years, founding multiple successful startups. His vision and leadership have been instrumental in our growth.",
    image:
      "https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600&q=80",
  },
  {
    name: "<PERSON><PERSON>",
    role: "UI/UX Designer",
    bio: "With a background in psychology and design, Amil brings a unique perspective to our product development process, ensuring our solutions are both beautiful and functional.",
    image:
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600&q=80",
  },
];

export default function AlternatingSections() {
  return (
    <div className="container mx-auto px-4 md:px-6 2xl:max-w-[1400px] py-24 lg:py-32">
      {/* Title */}
      <div className="max-w-2xl mx-auto text-center mb-10 lg:mb-14">
        <h2 className="text-3xl font-bold md:text-4xl md:leading-tight">
          Leadership team
        </h2>
        <p className="mt-1 text-lg text-muted-foreground">
          Meet the people shaping our future
        </p>
      </div>
      {/* End Title */}

      {/* Alternating Sections */}
      <div className="space-y-24">
        {team.map((member, index) => (
          <div
            key={member.name}
            className="grid md:grid-cols-2 items-center gap-8 lg:gap-12"
          >
            <div
              className={`relative ${index % 2 === 1 ? "md:order-last" : ""}`}
            >
              <div className="relative aspect-[4/3]">
                <Image
                  className="rounded-xl object-cover"
                  src={member.image}
                  alt={member.name}
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>
              {/* Background Pattern */}
              <div
                className={`absolute -z-10 size-48 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#1f2937_1px,transparent_1px)] [background-size:16px_16px] opacity-30 ${
                  index % 2 === 1 ? "-bottom-6 -left-6" : "-bottom-6 -right-6"
                }`}
              />
            </div>

            <div className="space-y-6">
              <div>
                <h3 className="text-2xl font-bold">{member.name}</h3>
                <p className="text-lg text-muted-foreground mt-1">
                  {member.role}
                </p>
              </div>

              <p className="text-muted-foreground">{member.bio}</p>

              <div className="flex gap-2">
                <Button size="icon" variant="ghost">
                  <Twitter className="size-4" />
                </Button>
                <Button size="icon" variant="ghost">
                  <Github className="size-4" />
                </Button>
                <Button size="icon" variant="ghost">
                  <Linkedin className="size-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
      {/* End Alternating Sections */}
    </div>
  );
}
