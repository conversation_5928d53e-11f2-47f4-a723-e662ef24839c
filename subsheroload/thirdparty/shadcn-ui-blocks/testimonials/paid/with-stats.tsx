import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ArrowUp } from "lucide-react";

export default function WithStats() {
  return (
    <div className="container mx-auto px-4 py-24 md:px-6 lg:py-32 2xl:max-w-[1400px]">
      {/* Grid */}
      <div className="lg:grid lg:grid-cols-12 lg:items-center lg:justify-between lg:gap-16">
        <div className="lg:col-span-5 lg:col-start-1">
          {/* Title */}
          <div className="mb-8">
            <h2 className="mb-2 text-3xl font-bold lg:text-4xl">
              It&apos;s all about speed
            </h2>
            <p className="text-muted-foreground">
              We provide you with a test account that can be set up in seconds.
              Our main focus is getting responses to you as soon as we can.
            </p>
          </div>
          {/* End Title */}

          {/* Blockquote */}
          <blockquote className="relative">
            <svg
              className="text-muted-foreground/15 absolute start-0 top-0 size-16 -translate-x-6 -translate-y-8 transform"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                d="M7.39762 10.3C7.39762 11.0733 7.14888 11.7 6.6514 12.18C6.15392 12.6333 5.52552 12.86 4.76621 12.86C3.84979 12.86 3.09047 12.5533 2.48825 11.94C1.91222 11.3266 1.62421 10.4467 1.62421 9.29999C1.62421 8.07332 1.96459 6.87332 2.64535 5.69999C3.35231 4.49999 4.33418 3.55332 5.59098 2.85999L6.4943 4.25999C5.81354 4.73999 5.26369 5.27332 4.84476 5.85999C4.45201 6.44666 4.19017 7.12666 4.05926 7.89999C4.29491 7.79332 4.56983 7.73999 4.88403 7.73999C5.61716 7.73999 6.21938 7.97999 6.69067 8.45999C7.16197 8.93999 7.39762 9.55333 7.39762 10.3ZM14.6242 10.3C14.6242 11.0733 14.3755 11.7 13.878 12.18C13.3805 12.6333 12.7521 12.86 11.9928 12.86C11.0764 12.86 10.3171 12.5533 9.71484 11.94C9.13881 11.3266 8.85079 10.4467 8.85079 9.29999C8.85079 8.07332 9.19117 6.87332 9.87194 5.69999C10.5789 4.49999 11.5608 3.55332 12.8176 2.85999L13.7209 4.25999C13.0401 4.73999 12.4903 5.27332 12.0713 5.85999C11.6786 6.44666 11.4168 7.12666 11.2858 7.89999C11.5215 7.79332 11.7964 7.73999 12.1106 7.73999C12.8437 7.73999 13.446 7.97999 13.9173 8.45999C14.3886 8.93999 14.6242 9.55333 14.6242 10.3Z"
                fill="currentColor"
              />
            </svg>

            <div className="relative z-10">
              <p className="text-xl italic">
                Amazing people to work with. Very fast and professional partner.
              </p>
            </div>

            <footer className="mt-6">
              <div className="flex items-center gap-x-4">
                <div className="shrink-0">
                  <Avatar className="size-8">
                    <AvatarImage
                      src="https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&w=320&h=320&q=80w=320&h=320&q=80"
                      alt="Avatar"
                    />
                    <AvatarFallback>CN</AvatarFallback>
                  </Avatar>
                </div>
                <div className="grow">
                  <div className="font-semibold">Josh Grazioso</div>
                  <div className="text-muted-foreground text-xs">
                    Director Payments &amp; Risk | Airbnb
                  </div>
                </div>
              </div>
            </footer>
          </blockquote>
          {/* End Blockquote */}
        </div>
        {/* End Col */}

        <div className="mt-10 lg:col-span-6 lg:col-end-13 lg:mt-0">
          <div className="space-y-6 sm:space-y-8">
            {/* List */}
            <ul className="divide-border grid grid-cols-2 divide-x divide-y overflow-hidden">
              <li className="-m-0.5 flex flex-col p-4 sm:p-8">
                <div className="mb-2 flex items-end gap-x-2 text-3xl font-bold sm:text-5xl">
                  45k+
                </div>
                <p className="text-muted-foreground text-sm sm:text-base">
                  users - from new startups to public companies
                </p>
              </li>

              <li className="-m-0.5 flex flex-col p-4 sm:p-8">
                <div className="mb-2 flex items-end gap-x-2 text-3xl font-bold sm:text-5xl">
                  <ArrowUp className="size-5 shrink-0 text-blue-600" />
                  23%
                </div>
                <p className="text-muted-foreground text-sm sm:text-base">
                  increase in traffic on webpages with Looms
                </p>
              </li>

              <li className="-m-0.5 flex flex-col p-4 sm:p-8">
                <div className="mb-2 flex items-end gap-x-2 text-3xl font-bold sm:text-5xl">
                  <ArrowUp className="size-5 shrink-0 text-blue-600" />
                  9.3%
                </div>
                <p className="text-muted-foreground text-sm sm:text-base">
                  boost in reply rates across sales outreach
                </p>
              </li>

              <li className="-m-0.5 flex flex-col p-4 sm:p-8">
                <div className="mb-2 flex items-end gap-x-2 text-3xl font-bold sm:text-5xl">
                  2x
                </div>
                <p className="text-muted-foreground text-sm sm:text-base">
                  faster than previous Acme versions
                </p>
              </li>
            </ul>
            {/* End List */}
          </div>
        </div>
        {/* End Col */}
      </div>
      {/* End Grid */}
    </div>
  );
}
