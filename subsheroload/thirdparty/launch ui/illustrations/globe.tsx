import { cn } from "@/lib/utils";
import * as React from "react";
import Glow from "../ui/glow";

export default function GlobeIllustration({
  className,
}: {
  className?: string;
}) {
  return (
    <div data-slot="globe-illustration" className={cn(className, "relative")}>
      <div className="relative z-10">
        <svg
          width={512}
          height={512}
          viewBox="0 0 512 512"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M84.8633 271.514L84.8705 271.652L85.2168 272.751L83.7305 283.163L82.2079 256.889L83.3697 245.99L84.8633 271.514ZM204.822 94.0793L203.942 94.8215L187.259 98.7579L187.736 98.5687L208.82 90.3976L221.497 87.6327L204.822 94.0793ZM290.796 86.5995L289.295 86.7159L284.395 85.2607L303.336 88.8405L304.267 89.1607L290.796 86.5995ZM224.015 423.803L224.795 424.13L256.132 429.289L241.78 430.766L211.785 425.84L224.015 423.803ZM206.994 425.055L174.365 411.201L185.405 407.337L185.499 407.403L222.118 422.995L208.387 425.28L206.994 425.055ZM114.714 359.257L120.299 349.769L149.154 381.267L140.56 387.903L139.709 387.299L116.742 362.24L114.714 359.257ZM98.479 313.104L119.534 348.917L114.079 358.173L95.2393 326.092L95.0733 325.561L98.479 313.104ZM97.4472 311.293L97.4833 311.409L97.7287 311.824L94.4672 323.64L84.1995 291.203L84.0552 288.693L85.9889 275.167L97.4472 311.293ZM147.906 124.493L145.525 126.247L148.202 123.999L147.906 124.493ZM235.185 84.664L236.809 84.5476L260.418 89.7136L238.432 98.2558L238.086 98.5468L208.798 94.916L235.185 84.664ZM387.066 148.832L371.379 131.849L374.143 126.072L394.469 148.024L387.066 148.832ZM420.77 202.173L406.151 189.767L406.122 189.44L399.599 170.275L411.577 175.688L411.844 176.132L417.523 192.7L420.77 202.173ZM427.632 223.223L429.912 248.296L426.246 234.493L426.441 233.482L426.47 233.329L424.745 214.302L427.632 223.223ZM425.618 285.659L421.13 260.76L421.448 260.178L425.777 237.011L430.503 254.837L430.778 257.857L425.618 285.659ZM333.259 410.561L313.07 419.365L324.702 412.409L325.777 412.299L325.943 412.278L342.834 404.922L333.259 410.561ZM267.879 428.067L286.445 416.309L287 416.403L322.082 412.685L305.082 422.85L301.301 424.494L267.879 428.067ZM97.1658 212.265L99.4387 250.224L99.446 250.363L99.5469 250.661L86.9269 269.069L84.7984 232.747L85.0942 231.459L97.1658 212.265ZM103.934 179.028L97.0432 209.471L97.0071 209.624L97.0576 210.424L85.8302 228.229L92.7716 197.691L103.934 179.028ZM164.913 141.265L164.826 141.418L164.776 141.629L133.734 152.586L150.308 124.77L151.023 124.173L179.856 116.017L164.913 141.265ZM218.135 116.068L205.746 137.219L167.575 141.098L182.649 115.617L182.836 115.464L218.135 116.068ZM291.293 106.631L290.889 106.791L265.707 90.6814L290.673 88.8332L291.257 88.9496L309.051 105.277L291.293 106.631ZM350.808 117.428L331.982 109.104L331.426 108.995L321.88 95.3672L347.597 106.718L347.9 106.943L350.808 117.428ZM370.491 131.173L352.474 118.163L352.114 118.003L349.328 107.969L373.313 125.25L370.491 131.173ZM410.134 317.106L388.776 336.584L389.894 300.328L389.974 300.299L407.746 284.204L410.365 316.677L410.134 317.106ZM377.173 370.855L346.507 384.148L359.004 352.323L387.058 340.317L377.628 370.44L377.173 370.855ZM112.622 291.698L112.65 291.778L99.114 309.255L87.0496 271.245L87.0279 270.859L99.9582 251.971L112.622 291.698ZM122.579 189.454L122.55 189.614L122.565 189.876L99.6769 207.79L107.484 173.294L108.04 172.377L130.127 155.759L122.579 189.454ZM157.683 173.862L157.654 173.985L125.061 188.472L132.702 154.325L132.847 154.078L164.494 142.902L157.683 173.862ZM424.247 233.22L419.723 257.435L404.21 229.335L404.231 229.298L407.601 211.792L424.197 232.704L424.247 233.22ZM395.371 245.444L395.392 245.422L403.582 230.477L419.399 259.138L419.348 259.421L407.998 280.318L395.371 245.444ZM407.277 281.65L389.606 297.643L381.871 257.544L394.483 246.237L407.298 281.613L407.277 281.65ZM387.564 337.698L359.618 349.703L365.693 310.609L388.776 300.808L387.643 337.618L387.564 337.698ZM344.97 384.81L308.914 388.543L325.055 355.75L357.763 352.468L345.107 384.752L344.97 384.81ZM325.38 410.109L288.761 413.98L307.521 390.879L307.666 390.908L343.549 387.19L326.022 409.826L325.38 410.109ZM167.597 365.572L203.891 391.526L186.559 405.445L151.687 380.554L167.597 365.572ZM136.318 331.622L166.767 364.838L150.842 379.834L121.605 347.95L136.318 331.622ZM113.242 292.804L135.603 330.771L120.963 347.011L99.5253 310.529L113.242 292.804ZM125.018 229.182L101.546 248.602L99.2222 209.813L99.2944 209.5L122.644 191.193L125.018 229.182ZM250.259 142.327L246.506 160.212L202.318 163.515L207.744 138.325L250.259 142.327ZM250.569 141.25L208.271 137.27L220.545 116.33L259.191 126.349L250.569 141.25ZM351.161 119.982L348.434 140.937L338.671 136.615L338.541 136.594L332.502 111.753L351.161 119.982ZM367.382 157.258L359.567 148.781L370.188 133.814L384.749 149.545L367.382 157.258ZM373.803 167.764L368.031 158.182L385.471 150.411L396.352 168.375L373.803 167.764ZM377.577 178.839L374.229 168.885L396.994 169.46L403.474 188.414L377.577 178.839ZM405.725 209.886L402.456 227.123L376.56 199.677L378.299 190.917L405.725 209.849V209.886ZM365.816 308.149L364.625 264.762L380.853 258.024L388.646 298.458L365.816 308.149ZM358.449 350.169L325.928 353.429L337.798 313.33L364.56 310.754L358.449 350.169ZM307.644 388.652L268.954 382.155L288.948 349.587L323.915 355.582L307.644 388.652ZM125.371 230.317L137.883 269.281L114.397 290.003L101.632 249.962L125.371 230.317ZM160.28 210.599L171.659 245.728L139.745 267.862L127.262 228.993L160.28 210.599ZM202.261 194.104L211.612 222.779L173.586 244.527L162.286 209.653L202.261 194.104ZM307.99 138.98L275.852 115.049L291.618 108.828L316.021 135.815L307.99 138.98ZM394.144 243.116L371.935 208.176L376.055 200.754L402.197 228.432L394.144 243.116ZM338.289 311.053L344.783 266.741L363.543 265.017L364.734 308.506L338.289 311.053ZM324.774 353.472L290.016 347.55L308.135 308.229L336.694 313.214L324.774 353.472ZM267.821 381.907L229.398 365.478L253.166 334.292L287.88 349.23L267.821 381.907ZM227.941 367.239L266.45 383.734L246.146 407.359L206.59 390.5L227.941 367.239ZM138.41 270.285L160.302 307.327L137.205 329.148L114.801 291.102L138.41 270.285ZM172.179 246.71L191.95 280.093L161.976 305.828L140.163 268.909L172.179 246.71ZM212.11 223.761L228.302 250.996L193.667 278.667L174.026 245.546L212.11 223.761ZM248.195 182.055L254.804 202.144L213.596 221.746L204.324 193.442L248.195 182.055ZM295.551 154.522L293.74 163.399L248.75 160.226L252.423 142.727L295.551 154.522ZM300.133 146.067L295.911 153.467L252.856 141.701L261.298 127.113L300.133 146.067ZM300.775 145.15L262.114 126.291L274.676 115.551L307.096 139.693L300.775 145.15ZM339.609 170.522L338.815 139.104L347.979 143.135L339.609 170.522ZM343.375 175.07L375.449 179.45L376.235 188.552L343.375 175.07ZM364.871 214.528L371.148 208.969L393.502 244.149L381.156 255.216L364.871 214.528ZM337.177 311.053L308.957 306.149L323.684 263.089L343.686 266.661L337.177 311.053ZM288.934 347.273L254.458 332.466L278.753 295.649L307.074 307.902L288.934 347.273ZM228.403 364.961L193.09 339.713L220.285 310.863L252.207 333.74L228.403 364.961ZM191.553 341.307L226.974 366.664L205.558 389.998L169.184 364.015L191.553 341.307ZM160.995 308.186L190.723 340.579L168.347 363.295L137.847 330.058L160.995 308.186ZM192.635 280.959L219.448 310.143L192.253 338.986L162.625 306.723L192.635 280.959ZM228.987 251.869L250.893 275.691L220.949 308.513L194.323 279.562L228.987 251.869ZM255.288 203.134L266.674 222.211L230.062 249.642L214.051 222.757L255.288 203.134ZM335.02 170.202L302.745 146.373L308.755 141.178L335.02 170.202ZM336.016 169.649L309.772 140.654L317.378 137.619L336.016 169.649ZM337.235 169.554L318.561 137.459L327.263 136.768L337.235 169.554ZM340.525 171.271L348.939 143.746L357.395 149.836L340.525 171.271ZM341.333 172.043L358.218 150.578L365.361 158.298L341.333 172.043ZM342.105 172.872L366.061 159.179L371.386 167.983L342.105 172.872ZM371.949 169.016L375.102 178.293L342.834 173.876L371.949 169.016ZM343.541 176.336L376.286 189.775L374.669 198.127L343.541 176.336ZM355.851 218.333L363.904 215.059L380.247 255.878L364.365 262.528L355.851 218.333ZM307.904 305.85L279.915 293.765L302.788 254.153L322.623 262.79L307.904 305.85ZM267.352 223.085L282.721 239.783L252.373 274.039L230.719 250.523L267.352 223.085ZM294.678 175.368L297.99 185.373L256.839 201.271L250.345 181.64L294.678 175.368ZM343.253 177.485L374.366 199.255L370.455 206.306L343.253 177.485ZM345.461 219.439L354.783 218.624L363.305 262.812L344.948 264.493L345.461 219.439ZM334.414 217.569L344.371 219.396L343.859 264.442L324.283 260.971L334.414 217.569ZM298.466 186.377L304.159 195.908L268.463 220.916L257.315 202.275L298.466 186.377ZM300.075 184.667L296.914 175.208L334.984 174.553L300.075 184.667ZM362.901 213.08L355.252 216.23L341.499 178.751L362.901 213.08ZM342.531 178.322L369.799 207.208L363.832 212.498L342.531 178.322ZM304.83 196.781L312.522 205.142L284.179 238.11L269.134 221.797L304.83 196.781ZM305.992 194.671L300.587 185.671L335.669 175.513L305.992 194.671ZM345.338 217.22L340.323 178.751L354.163 216.449L345.338 217.22ZM313.958 203.439L306.67 195.552L336.42 176.343L313.958 203.439ZM339.169 178.388L344.22 217.111L334.796 215.416L339.169 178.388ZM324.37 210.955L338.13 177.798L333.728 215.044L324.37 210.955ZM323.417 210.38L314.78 204.174L337.228 177.1L323.417 210.38ZM302.874 251.665L285.009 238.837L313.359 205.855L322.486 212.432L302.874 251.665ZM323.237 260.644L303.827 252.218L323.461 212.949L333.353 217.293L323.237 260.644ZM278.955 293.226L253.202 274.767L283.558 240.504L301.814 253.63L278.955 293.226ZM334.464 173.454L296.633 174.102L295.904 164.854L334.464 173.454ZM298.004 154.245L302.023 147.217L334.414 171.126L298.004 154.245ZM334.24 172.268L295.919 163.719L297.643 155.3L334.24 172.268ZM253.484 331.935L221.786 309.248L251.73 276.411L277.786 295.111L253.484 331.935ZM338.49 169.896L328.468 136.95L337.704 138.667L338.49 169.896ZM293.667 164.505L294.439 174.277L250.107 180.556L248.678 161.325L293.667 164.505ZM352.178 120.681L369.373 133.065L358.709 148.075L349.48 141.41L352.178 120.681ZM246.47 161.318L247.935 180.985L204.129 192.35L202.188 164.628L246.47 161.318ZM287.058 414.162L247.387 407.585L267.547 384.141L306.273 390.668L287.246 414.14L287.058 414.162ZM403.943 189.775L405.602 208.445L378.537 189.731L378.544 189.702L377.714 180.068L403.921 189.724L403.943 189.775ZM331.239 111.193L337.358 136.368L327.552 134.52L327.501 134.527L312.349 107.642L331.217 111.185L331.239 111.193ZM310.934 107.373L326.289 134.622L317.248 135.335L317.125 135.386L293.011 108.719L310.869 107.365L310.934 107.373ZM200.009 164.97L201.972 193.027L162.128 208.532L159.884 174.407L200.009 164.97ZM157.726 175.157L159.985 209.507L127.139 227.807L124.765 189.818L157.726 175.157ZM225.401 421.984L187.663 405.94L204.865 392.152L244.255 408.96L225.617 422.02L225.401 421.984ZM263.867 90.8197L289.634 107.278L273.63 113.543L273.529 113.63L241.174 99.5582L263.47 90.8486L263.867 90.8197ZM239.709 100.133L272.605 114.416L259.79 125.366L221.548 115.442L239.565 100.191L239.709 100.133ZM205.486 138.354L199.995 163.835L160.078 173.236L166.89 142.283L205.486 138.354ZM227.176 422.275L245.453 409.469L245.496 409.484L284.807 416.032L265.39 428.336L264.545 428.423L227.176 422.275ZM358.543 395.659L328.064 408.938L345.071 387.03L345.36 387.001L375.413 374.027L360.556 393.818L358.543 395.659ZM379.244 368.956L388.393 339.75L388.769 339.589L409.766 320.431L403.156 346.16L402.226 347.892L379.244 368.956ZM411.324 314.894L408.777 283.272L409.023 283.047L420.293 262.31L425.048 288.715L424.832 289.872L411.324 314.894ZM421.679 204.829L424.017 230.703L407.875 210.366L407.947 210.002L406.288 191.339L421.469 204.218L421.679 204.829ZM206.619 95.7674L236.931 99.5218L219.015 114.693L218.871 114.816L218.777 114.969L184.135 114.372L205.955 96.0221L206.619 95.7674ZM130.559 138.769L132.183 137.416L146.455 126.931L130.632 153.489L130.495 154.093L109.54 169.86L125.682 142.858L130.559 138.769ZM172.576 410.444L164.473 404.965L141.498 388.572L149.977 382.024L184.301 406.551L172.85 410.561L172.576 410.444ZM384.418 371.91L368.262 386.739L364.993 389.736L377.455 373.147L378.378 372.747L395.32 357.219L384.418 371.91ZM418.554 317.572L411.332 330.982L405.328 342.129L411.245 319.078L411.887 318.496L423.028 297.854L418.554 317.572ZM395.428 149.057L410.56 174.014L399.108 168.834L399.022 168.572L387.722 149.872L395.407 149.043L395.428 149.057ZM319.355 94.2539L319.932 94.5086L329.875 108.704L310.992 105.132L310.53 105.168L293.307 89.3353L319.355 94.2539ZM273.579 83.2161L286.17 86.9487L263.146 88.6513L262.287 88.986L240.698 84.2638L268.37 82.2339L273.579 83.2161ZM176.263 103.022L179.77 101.661L202.059 96.4076L180.953 114.161L180.672 114.634L153.101 122.434L176.263 103.022ZM171.443 412.35L206.258 427.15L241.715 433L264.48 430.664L264.618 430.65H264.625L301.871 426.67L334.19 412.569C334.197 412.569 334.197 412.569 334.197 412.561L359.647 397.602L359.755 397.551L385.976 373.482L403.921 349.339L403.979 349.281L420.56 318.496C420.647 318.336 420.676 318.154 420.683 317.979L426.924 290.65L426.932 290.621L426.968 290.461V290.454L432.971 258.119L433 257.966L429.811 222.903C429.796 222.743 429.746 222.59 429.674 222.459L423.8 204.225L413.886 175.295L397.247 147.806L375.059 123.809L348.78 104.848L320.12 92.1657H320.105L320.033 92.1294L303.957 86.7159L303.863 86.694C303.848 86.694 303.834 86.6795 303.812 86.6795L268.492 80L234.861 82.4666L208.271 88.2439L208.17 88.2803C208.148 88.2875 208.127 88.2875 208.105 88.2948L175.577 100.912L175.303 101.013V101.021L175.137 101.086L174.603 101.53L151.189 118.614C151.182 118.614 151.182 118.614 151.182 118.622L139.139 128.706L124.022 141.367L123.993 141.389L123.849 141.629L108.48 167.124L90.8017 196.694L90.7079 196.847L82.6192 232.405L82.5832 232.558L82.5903 232.667L80 256.831L82.0131 291.473L82.0204 291.611L93.1829 326.885L112.845 360.428L115.768 364.736L137.357 388.31L163.224 406.792L171.443 412.35Z"
            fill="url(#paint0_linear_214_5932)"
            className="opacity-30 dark:opacity-100"
          />
          <g filter="url(#filter0_d_214_5932)">
            <ellipse
              cx={256}
              cy={256}
              rx={118.704}
              ry={243.335}
              transform="rotate(-45 256 256)"
              stroke="url(#paint1_linear_214_5932)"
              strokeWidth={2}
              shapeRendering="crispEdges"
            />
            <ellipse
              cx={256}
              cy={256}
              rx={118.704}
              ry={243.335}
              transform="rotate(-45 256 256)"
              strokeWidth={12}
              strokeDasharray="0 1200"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="-sm stroke-light-foreground group-hover:animate-orbit opacity-0 [stroke-dashoffset:500]"
            />
          </g>
          <g filter="url(#filter1_d_214_5932)">
            <ellipse
              cx={263.941}
              cy={291.099}
              rx={86.5749}
              ry={216.161}
              transform="rotate(-105 263.941 291.099)"
              stroke="url(#paint2_linear_214_5932)"
              strokeWidth={2}
              shapeRendering="crispEdges"
            />
          </g>
          <defs>
            <filter
              id="filter0_d_214_5932"
              x={55.5125}
              y={55.5125}
              width={400.975}
              height={400.975}
              filterUnits="userSpaceOnUse"
              colorInterpolationFilters="sRGB"
            >
              <feFlood floodOpacity={0} result="BackgroundImageFix" />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset />
              <feGaussianBlur stdDeviation={4} />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0.992157 0 0 0 0 0.729412 0 0 0 0 0.454902 0 0 0 0.24 0"
              />
              <feBlend
                mode="normal"
                in2="BackgroundImageFix"
                result="effect1_dropShadow_214_5932"
              />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="effect1_dropShadow_214_5932"
                result="shape"
              />
            </filter>
            <filter
              id="filter1_d_214_5932"
              x={44.9285}
              y={181.474}
              width={438.026}
              height={219.249}
              filterUnits="userSpaceOnUse"
              colorInterpolationFilters="sRGB"
            >
              <feFlood floodOpacity={0} result="BackgroundImageFix" />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset />
              <feGaussianBlur stdDeviation={4} />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0.992157 0 0 0 0 0.729412 0 0 0 0 0.454902 0 0 0 0.24 0"
              />
              <feBlend
                mode="normal"
                in2="BackgroundImageFix"
                result="effect1_dropShadow_214_5932"
              />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="effect1_dropShadow_214_5932"
                result="shape"
              />
            </filter>
            <linearGradient
              id="paint0_linear_214_5932"
              x1={256.5}
              y1={80}
              x2={256.5}
              y2={433}
              gradientUnits="userSpaceOnUse"
            >
              <stop style={{ stopColor: "var(--brand)" }} stopOpacity={0.8} />
              <stop
                offset={1}
                style={{ stopColor: "var(--brand)" }}
                stopOpacity={0}
              />
            </linearGradient>
            <linearGradient
              id="paint1_linear_214_5932"
              x1={321.407}
              y1={74.6271}
              x2={187.057}
              y2={136.145}
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="var(--light-foreground)" />
              <stop offset={1} stopColor="var(--brand)" stopOpacity={0} />
            </linearGradient>
            <linearGradient
              id="paint2_linear_214_5932"
              x1={345.488}
              y1={199.648}
              x2={209.085}
              y2={187.891}
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="var(--light-foreground)" stopOpacity={0.7} />
              <stop offset={0.395} stopColor="var(--brand)" stopOpacity={0.1} />
              <stop offset={1} stopColor="var(--brand)" stopOpacity={0} />
            </linearGradient>
          </defs>
        </svg>
        <Glow
          variant="center"
          className="opacity-20 transition-all duration-300 group-hover:opacity-30"
        />
      </div>
    </div>
  );
}
