const TypeScript = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21 1C22.1046 1 23 1.89232 23 2.99689C23 8.68454 23 17.1774 23 21.0041C23 22.1086 22.1077 23 21.0031 23C15.3155 23 6.82263 23 2.99595 23C1.89137 23 1.00001 22.108 1.00002 21.0034C1.00007 15.2015 1.00003 6.79323 1.00002 2.99497C1.00001 1.8904 1.89544 1 3.00002 1L21 1ZM13.2297 12.7043V10.9H5.4V12.7043H8.19532V20.7382H10.4206V12.7043H13.2297ZM14.1168 20.5261C14.4757 20.709 14.9001 20.8463 15.39 20.9378C15.8801 21.0292 16.3964 21.075 16.9393 21.075C17.4684 21.075 17.971 21.0247 18.4472 20.924C18.9234 20.8234 19.3409 20.6576 19.6997 20.4266C20.0585 20.1956 20.3427 19.8938 20.552 19.521C20.7613 19.1481 20.866 18.6873 20.866 18.1384C20.866 17.7405 20.8062 17.3918 20.6865 17.0922C20.567 16.7925 20.3944 16.5261 20.169 16.2928C19.9436 16.0595 19.6733 15.8503 19.3581 15.665C19.0429 15.4798 18.6876 15.3048 18.292 15.1402C18.0021 15.0212 17.7421 14.9057 17.5121 14.7937C17.282 14.6816 17.0865 14.5672 16.9255 14.4506C16.7645 14.3339 16.6403 14.2105 16.5529 14.0801C16.4655 13.9498 16.4218 13.8022 16.4218 13.6376C16.4218 13.4866 16.4609 13.3505 16.5391 13.2294C16.6173 13.1081 16.7277 13.004 16.8703 12.9171C17.013 12.8302 17.1877 12.7628 17.3948 12.7147C17.6018 12.6668 17.8319 12.6427 18.0849 12.6427C18.2689 12.6427 18.4633 12.6564 18.668 12.6839C18.8728 12.7114 19.0787 12.7537 19.2856 12.8108C19.4927 12.868 19.694 12.94 19.8895 13.027C20.0851 13.1139 20.2656 13.2145 20.4313 13.3288V11.2774C20.0953 11.1493 19.7285 11.0543 19.3305 10.9927C18.9326 10.9309 18.4759 10.9 17.9606 10.9C17.4362 10.9 16.9393 10.956 16.4701 11.0681C16.0008 11.1802 15.5879 11.3551 15.2313 11.593C14.8748 11.8309 14.593 12.1339 14.386 12.502C14.1789 12.8703 14.0754 13.3105 14.0754 13.8228C14.0754 14.4769 14.2652 15.035 14.6448 15.4969C15.0244 15.9589 15.6005 16.35 16.3734 16.6702C16.6771 16.7937 16.96 16.9149 17.2223 17.0339C17.4845 17.1527 17.7111 17.2763 17.902 17.4043C18.0929 17.5324 18.2436 17.6719 18.354 17.8228C18.4645 17.9738 18.5197 18.1453 18.5197 18.3374C18.5197 18.4793 18.4851 18.6107 18.4161 18.7319C18.3471 18.8532 18.2424 18.9583 18.1021 19.0475C17.9618 19.1367 17.787 19.2065 17.5777 19.2568C17.3684 19.3071 17.1233 19.3323 16.8427 19.3323C16.3642 19.3323 15.8904 19.2488 15.4211 19.0819C14.9519 18.9149 14.5171 18.6645 14.1168 18.3306V20.5261Z"
      fill="currentColor"
    />
  </svg>
);
export default TypeScript;
