import * as React from "react";
import { SVGProps } from "react";
const PictelA<PERSON> = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={123}
    height={36}
    viewBox="0 0 123 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g opacity={0.84}>
      <rect
        x={15.4307}
        y={15.4285}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.6}
        x={15.4307}
        y={20.5713}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.32}
        x={20.5693}
        y={20.5713}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        x={25.7139}
        y={20.5713}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.32}
        x={10.2861}
        y={20.5713}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        x={5.14307}
        y={20.5713}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.32}
        x={15.4307}
        y={25.7144}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        x={10.2861}
        y={25.7144}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        x={20.5693}
        y={25.7144}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        x={15.4307}
        y={30.8572}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.6}
        x={10.2861}
        y={15.4285}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.32}
        x={5.14307}
        y={15.4285}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        y={15.4285}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.6}
        x={20.5693}
        y={15.4285}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.32}
        x={25.7139}
        y={15.4285}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        x={30.8569}
        y={15.4285}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.6}
        x={15.4307}
        y={10.2856}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.32}
        x={10.2861}
        y={10.2856}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        x={10.2861}
        y={5.14282}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        x={5.14307}
        y={10.2856}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.32}
        x={20.5693}
        y={10.2856}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        x={20.5693}
        y={5.14282}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        x={25.7139}
        y={10.2856}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.32}
        x={15.4307}
        y={5.14282}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
      <rect
        opacity={0.07}
        x={15.4307}
        width={5.14285}
        height={5.14285}
        fill="currentColor"
      />
    </g>
    <g opacity={0.84}>
      <path
        d="M42 24.7501V10.8466H47.9865C52.335 10.8466 54.207 12.6016 54.207 15.6436C54.207 18.6271 52.218 20.4211 48.1815 20.4016H44.808V24.7501H42ZM48.006 13.2646H44.808V17.9836H48.084C50.3265 17.9836 51.3405 17.2426 51.3405 15.6241C51.3405 13.9666 50.268 13.2646 48.006 13.2646Z"
        fill="currentColor"
      />
      <path
        d="M58.3859 24.7501H55.4609V13.5376H58.3859V24.7501Z"
        fill="currentColor"
      />
      <path
        d="M59.6007 19.1341C59.6007 15.3706 62.2527 13.2256 65.9772 13.2256C69.6237 13.2256 71.9442 14.8831 72.2757 18.1396V18.1591H69.2337V18.1201C68.9802 16.4236 67.8882 15.6046 65.9967 15.6046C63.8322 15.6046 62.5452 16.8136 62.5452 19.1341V19.2901C62.5452 21.4546 63.7152 22.6441 65.9772 22.6441C67.9857 22.6441 68.9997 21.7861 69.2532 20.0701V20.0506H72.2757V20.0896C72.0222 23.3461 69.7992 25.0816 65.9187 25.0816C61.6482 25.0816 59.6007 22.9561 59.6007 19.3096V19.1341Z"
        fill="currentColor"
      />
      <path
        d="M74.8625 16.0336H72.2885V13.5376H75.116C75.116 13.5376 75.1355 10.9246 75.116 10.5151H77.534V13.5376H81.2195V16.0336H77.534V19.3681C77.534 20.2066 77.612 20.8111 77.7095 21.2206C77.9435 22.0006 78.626 22.1761 80.0495 22.1761H81.2195V24.7501H79.3085C75.779 24.7501 74.8625 23.4631 74.8625 19.8556V16.0336Z"
        fill="currentColor"
      />
      <path
        d="M81.5778 19.2121C81.5778 15.4486 84.2493 13.2256 87.9933 13.2256C89.3973 13.2256 90.5478 13.4791 91.4643 14.0056C93.2973 15.0586 94.2333 16.8331 94.2333 18.9976V20.1871H84.3468C84.6393 21.8836 85.8093 22.7806 88.0128 22.7806C89.6313 22.7806 90.5283 22.3126 90.9768 21.4351H94.0968C93.2193 23.8726 91.0743 25.0816 87.8568 25.0816C84.1518 25.0816 81.5778 23.4631 81.5778 19.3876V19.2121ZM84.3858 18.0421H91.4058C91.1523 16.3456 90.0798 15.4486 88.0518 15.4486C85.9653 15.4486 84.7563 16.4041 84.3858 18.0421Z"
        fill="currentColor"
      />
      <path
        d="M98.3207 24.7501H95.4542V9.81309H98.3207V24.7501Z"
        fill="currentColor"
      />
      <path
        d="M98.931 24.7501L104.781 10.8466H107.745L113.712 24.7501H110.768L109.5 21.6691H103.124L101.915 24.7501H98.931ZM104.06 19.3096H108.525L106.263 13.7716L104.06 19.3096Z"
        fill="currentColor"
      />
      <path
        d="M117.188 13.3816H114.126V10.8466H122.94V13.3816H119.898V22.0981H122.94V24.7501H114.126V22.0981H117.188V13.3816Z"
        fill="currentColor"
      />
      <path
        d="M58.8525 10.4101C58.8525 11.4498 58.0097 12.2926 56.97 12.2926C55.9303 12.2926 55.0875 11.4498 55.0875 10.4101C55.0875 9.37041 55.9303 8.52759 56.97 8.52759C58.0097 8.52759 58.8525 9.37041 58.8525 10.4101Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PictelAI;
