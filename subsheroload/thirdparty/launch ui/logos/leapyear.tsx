import * as React from "react";
import { SVGProps } from "react";
const LeapYear = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={123}
    height={36}
    viewBox="0 0 123 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g opacity={0.84}>
      <path d="M16.4985 14V6H0.498535V14L16.4985 14Z" fill="currentColor" />
      <path
        d="M16.4982 21.9999C12.0799 21.9999 8.49821 25.5816 8.49821 29.9999L0.499513 30.0002L0.499513 29.7938C0.609457 21.0865 7.6748 14.0552 16.3953 14.0002L24.4985 14.0006V30.0006H16.4985L16.4982 21.9999Z"
        fill="currentColor"
      />
    </g>
    <g opacity={0.84}>
      <path
        d="M32 24.75V9.98779H35.807V21.3885H42.4693V24.75H32Z"
        fill="currentColor"
      />
      <path
        d="M48.7708 24.9525C47.5423 24.9525 46.5163 24.723 45.6928 24.264C44.8693 23.7915 44.2483 23.13 43.8298 22.2795C43.4248 21.429 43.2223 20.4435 43.2223 19.323V19.1003C43.2223 17.9123 43.445 16.9133 43.8905 16.1033C44.336 15.2798 44.9503 14.652 45.7333 14.22C46.5298 13.788 47.441 13.572 48.467 13.572C49.6145 13.572 50.5865 13.788 51.383 14.22C52.193 14.6385 52.8073 15.2325 53.2258 16.002C53.6443 16.758 53.8535 17.649 53.8535 18.675V20.1735H46.847V20.6798C46.847 21.1388 47.0158 21.5168 47.3533 21.8138C47.6908 22.1108 48.1228 22.2593 48.6493 22.2593C49.1218 22.2593 49.5065 22.1648 49.8035 21.9758C50.1005 21.7733 50.276 21.51 50.33 21.186H53.732C53.6915 21.9285 53.4553 22.5833 53.0233 23.1503C52.5913 23.7173 52.0108 24.1628 51.2818 24.4868C50.5528 24.7973 49.7158 24.9525 48.7708 24.9525ZM46.847 17.9055V18.0068H50.33V17.9055C50.33 17.325 50.168 16.8998 49.844 16.6298C49.52 16.3463 49.1015 16.2045 48.5885 16.2045C48.0755 16.2045 47.657 16.353 47.333 16.65C47.009 16.9335 46.847 17.352 46.847 17.9055Z"
        fill="currentColor"
      />
      <path
        d="M58.8343 24.9525C57.8488 24.9525 57.0455 24.7298 56.4245 24.2843C55.817 23.8388 55.3715 23.211 55.088 22.401C54.8045 21.5775 54.6628 20.6258 54.6628 19.5458V19.0193C54.6628 17.0888 55.0408 15.705 55.7968 14.868C56.5528 14.0175 57.5653 13.5923 58.8343 13.5923C59.5228 13.5923 60.1438 13.7408 60.6973 14.0378C61.2643 14.3213 61.6828 14.7398 61.9528 15.2933H62.1148L62.297 13.7745H65.6788V24.75H62.1148V23.2515H61.9528C61.6828 23.7915 61.2643 24.21 60.6973 24.507C60.1438 24.804 59.5228 24.9525 58.8343 24.9525ZM60.2923 22.0568C60.8458 22.0568 61.2845 21.8948 61.6085 21.5708C61.9325 21.2468 62.0945 20.7743 62.0945 20.1533V18.3713C62.0945 17.7638 61.9325 17.298 61.6085 16.974C61.2845 16.6365 60.8458 16.4678 60.2923 16.4678C59.7118 16.4678 59.2595 16.6365 58.9355 16.974C58.625 17.3115 58.4698 17.7773 58.4698 18.3713V20.1938C58.4698 20.7743 58.625 21.2333 58.9355 21.5708C59.2595 21.8948 59.7118 22.0568 60.2923 22.0568Z"
        fill="currentColor"
      />
      <path
        d="M67.7795 28.1318V13.7745H71.1613L71.3638 15.2933H71.5258C71.7958 14.7398 72.2075 14.3213 72.761 14.0378C73.328 13.7408 73.9558 13.5923 74.6443 13.5923C75.6433 13.5923 76.4465 13.815 77.054 14.2605C77.6615 14.706 78.107 15.3405 78.3905 16.164C78.674 16.974 78.8158 17.9258 78.8158 19.0193V19.5458C78.8158 21.4628 78.4378 22.8465 77.6818 23.697C76.9258 24.534 75.9133 24.9525 74.6443 24.9525C73.9558 24.9525 73.328 24.804 72.761 24.507C72.2075 24.21 71.7958 23.7915 71.5258 23.2515H71.3638V28.1318H67.7795ZM73.1863 22.0568C73.7668 22.0568 74.2123 21.8948 74.5228 21.5708C74.8468 21.2333 75.0088 20.7743 75.0088 20.1938V18.3713C75.0088 17.7773 74.8468 17.3115 74.5228 16.974C74.2123 16.6365 73.7668 16.4678 73.1863 16.4678C72.6463 16.4678 72.2075 16.6365 71.87 16.974C71.546 17.298 71.384 17.7638 71.384 18.3713V20.1533C71.384 20.7743 71.546 21.2468 71.87 21.5708C72.2075 21.8948 72.6463 22.0568 73.1863 22.0568Z"
        fill="currentColor"
      />
      <path
        d="M79.2077 28.1318V25.3373H83.0147V23.5553L78.7014 13.7745H82.4274L84.3714 19.323L84.6954 20.376H84.7967L85.1207 19.323L87.1457 13.7745H90.8514L86.5787 23.5553V25.9245C86.5787 26.667 86.3627 27.2205 85.9307 27.585C85.5122 27.9495 84.9519 28.1318 84.2499 28.1318H79.2077Z"
        fill="currentColor"
      />
      <path
        d="M96.1542 24.9525C94.9257 24.9525 93.8997 24.723 93.0762 24.264C92.2527 23.7915 91.6317 23.13 91.2132 22.2795C90.8082 21.429 90.6057 20.4435 90.6057 19.323V19.1003C90.6057 17.9123 90.8285 16.9133 91.274 16.1033C91.7195 15.2798 92.3337 14.652 93.1167 14.22C93.9132 13.788 94.8245 13.572 95.8505 13.572C96.998 13.572 97.97 13.788 98.7665 14.22C99.5765 14.6385 100.191 15.2325 100.609 16.002C101.028 16.758 101.237 17.649 101.237 18.675V20.1735H94.2305V20.6798C94.2305 21.1388 94.3992 21.5168 94.7367 21.8138C95.0742 22.1108 95.5062 22.2593 96.0327 22.2593C96.5052 22.2593 96.89 22.1648 97.187 21.9758C97.484 21.7733 97.6595 21.51 97.7135 21.186H101.115C101.075 21.9285 100.839 22.5833 100.407 23.1503C99.9747 23.7173 99.3942 24.1628 98.6652 24.4868C97.9362 24.7973 97.0992 24.9525 96.1542 24.9525ZM94.2305 17.9055V18.0068H97.7135V17.9055C97.7135 17.325 97.5515 16.8998 97.2275 16.6298C96.9035 16.3463 96.485 16.2045 95.972 16.2045C95.459 16.2045 95.0405 16.353 94.7165 16.65C94.3925 16.9335 94.2305 17.352 94.2305 17.9055Z"
        fill="currentColor"
      />
      <path
        d="M106.218 24.9525C105.232 24.9525 104.429 24.7298 103.808 24.2843C103.2 23.8388 102.755 23.211 102.471 22.401C102.188 21.5775 102.046 20.6258 102.046 19.5458V19.0193C102.046 17.0888 102.424 15.705 103.18 14.868C103.936 14.0175 104.949 13.5923 106.218 13.5923C106.906 13.5923 107.527 13.7408 108.081 14.0378C108.648 14.3213 109.066 14.7398 109.336 15.2933H109.498L109.68 13.7745H113.062V24.75H109.498V23.2515H109.336C109.066 23.7915 108.648 24.21 108.081 24.507C107.527 24.804 106.906 24.9525 106.218 24.9525ZM107.676 22.0568C108.229 22.0568 108.668 21.8948 108.992 21.5708C109.316 21.2468 109.478 20.7743 109.478 20.1533V18.3713C109.478 17.7638 109.316 17.298 108.992 16.974C108.668 16.6365 108.229 16.4678 107.676 16.4678C107.095 16.4678 106.643 16.6365 106.319 16.974C106.008 17.3115 105.853 17.7773 105.853 18.3713V20.1938C105.853 20.7743 106.008 21.2333 106.319 21.5708C106.643 21.8948 107.095 22.0568 107.676 22.0568Z"
        fill="currentColor"
      />
      <path
        d="M115.163 24.75V16.4678C115.163 15.6578 115.406 15.0098 115.892 14.5238C116.391 14.0243 117.039 13.7745 117.836 13.7745H122.291V16.65H118.747V24.75H115.163Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default LeapYear;
