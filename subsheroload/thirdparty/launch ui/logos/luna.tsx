const Luna = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.0209 4.16243C13.5392 4.24237 13.0672 4.351 12.6066 4.48651L12.6066 27.5135C13.0672 27.649 13.5392 27.7576 14.0209 27.8376L14.0209 4.16243ZM15.4351 27.9869C15.6223 27.9956 15.8106 28 16 28C22.6274 28 28 22.6274 28 16C28 9.37258 22.6274 4 16 4C15.8106 4 15.6223 4.00439 15.4351 4.01306L15.4351 27.9869ZM11.1924 26.9982L11.1924 5.00181C10.7031 5.21601 10.2308 5.4619 9.77819 5.7369L9.77819 26.2631C10.2308 26.5381 10.7031 26.784 11.1924 26.9982ZM8.36397 25.2574V6.74262C7.84178 7.17385 7.35669 7.64841 6.91421 8.16079L6.91421 23.8392C7.35669 24.3516 7.84178 24.8262 8.36397 25.2574ZM5.5 21.814V10.186C4.54425 11.9084 4 13.8906 4 16C4 18.1094 4.54425 20.0916 5.5 21.814Z"
      fill="currentColor"
    />
  </svg>
);
export default Luna;
