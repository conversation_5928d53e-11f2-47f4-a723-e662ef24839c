const Notion = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 800 800"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M50 290C50 205.992 50 163.988 66.349 131.902C80.73 103.677 103.677 80.73 131.902 66.349C163.988 50 205.992 50 290 50H510C594.008 50 636.013 50 668.097 66.349C696.323 80.73 719.27 103.677 733.65 131.902C750 163.988 750 205.992 750 290V510C750 594.008 750 636.013 733.65 668.097C719.27 696.323 696.323 719.27 668.097 733.65C636.013 750 594.008 750 510 750H290C205.992 750 163.988 750 131.902 733.65C103.677 719.27 80.73 696.323 66.349 668.097C50 636.013 50 594.008 50 510V290ZM206.667 250C189.178 250 175 265.99 175 285.715V458.927C175 509.225 211.153 550 255.75 550L443.332 548.215C460.822 548.215 475 532.225 475 512.5V337.5C475 287.202 432.098 250 387.5 250H206.667ZM500 362.5C500 345.528 506.49 329.38 517.805 318.19L588.22 259.58C602.547 245.414 625 256.888 625 278.38V522.513C625 544.003 602.547 555.478 588.22 541.313L517.805 479.293C506.49 468.105 500 451.955 500 434.983V362.5Z"
      fill="currentColor"
    />
  </svg>
);
export default Notion;
