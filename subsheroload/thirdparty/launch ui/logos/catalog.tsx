import * as React from "react";
import { SVGProps } from "react";
const Catalog = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={114}
    height={36}
    viewBox="0 0 114 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g opacity={0.84}>
      <path
        opacity={0.3}
        d="M15.0001 3C6.71572 3 0 9.71575 0 18.0001C0 26.2844 6.71572 33.0001 15.0001 33.0001C22.4326 33.0001 28.6025 27.5944 29.7927 20.5001H29.6851C28.575 24.8132 24.6597 28.0001 20.0001 28.0001C14.4772 28.0001 10 23.5229 10 18.0001C10 12.4772 14.4772 8.00002 20.0001 8.00002C24.6597 8.00002 28.575 11.187 29.6851 15.5H29.7927C28.6025 8.40574 22.4326 3 15.0001 3Z"
        fill="currentColor"
      />
      <path
        d="M0 18.0001C0 9.71575 6.71575 3 15.0001 3C22.4326 3 28.6025 8.40574 29.7927 15.5H19.685C18.5749 11.187 14.6597 8.00002 10 8.00002C4.47717 8.00002 0 12.4772 0 18.0001Z"
        fill="currentColor"
      />
      <path
        d="M0 18.0002C0 26.2845 6.71575 33.0002 15.0001 33.0002C22.4326 33.0002 28.6025 27.5945 29.7927 20.5002H19.685C18.5749 24.8133 14.6597 28.0002 10 28.0002C4.47717 28.0002 0 23.5231 0 18.0002Z"
        fill="currentColor"
      />
    </g>
    <g opacity={0.84}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M49.363 15.5264H52.3124L52.2992 15.4401C51.8139 12.2635 48.6864 9.98242 45.1563 9.98242C40.7635 9.98242 37.4253 13.3886 37.4253 17.8657C37.4253 22.364 40.7631 25.7924 45.1563 25.7924C48.7774 25.7924 51.7261 23.4864 52.3423 20.1413L52.3586 20.0527H49.3823L49.3682 20.1097C48.9209 21.9203 47.193 23.1629 45.1563 23.1629C42.3531 23.1629 40.207 20.8741 40.207 17.8657C40.207 14.8796 42.3525 12.6119 45.1563 12.6119C47.1728 12.6119 48.8578 13.791 49.3473 15.4724L49.363 15.5264Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M64.781 25.5749V14.7892H62.195V16.5085C61.4047 15.2787 60.1287 14.5717 58.4855 14.5717C55.5889 14.5717 53.321 17.0691 53.321 20.1712C53.321 23.2728 55.5885 25.7924 58.4855 25.7924C60.1287 25.7924 61.4047 25.0854 62.195 23.8556V25.5749H64.781ZM59.0075 23.3369C57.2443 23.3369 55.8635 21.9357 55.8635 20.1712C55.8635 18.4288 57.244 17.0272 59.0075 17.0272C60.8151 17.0272 62.1732 18.3859 62.1732 20.1712C62.1732 21.9785 60.8148 23.3369 59.0075 23.3369Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M70.3339 21.4544V17.2447H73.1179V14.7892H70.3339V11.9399H67.7479V14.7892H65.7251V17.2447H67.7479V21.4979C67.7479 22.8609 68.1438 23.9148 68.846 24.6282C69.5483 25.3416 70.548 25.7054 71.7379 25.7054C72.1327 25.7054 72.6827 25.6617 73.06 25.5729L73.1179 25.5593V23.1615L73.0231 23.1873C72.793 23.2501 72.5177 23.2717 72.2599 23.2717C71.6828 23.2717 71.2028 23.1379 70.8679 22.8494C70.535 22.5626 70.3339 22.1127 70.3339 21.4544Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M84.7901 25.5749V14.7892H82.2041V16.5085C81.4138 15.2787 80.1379 14.5717 78.4946 14.5717C75.5981 14.5717 73.3301 17.0691 73.3301 20.1712C73.3301 23.2728 75.5977 25.7924 78.4946 25.7924C80.1379 25.7924 81.4138 25.0854 82.2041 23.8556V25.5749H84.7901ZM79.0166 23.3369C77.2535 23.3369 75.8726 21.9357 75.8726 20.1712C75.8726 18.4288 77.2531 17.0272 79.0166 17.0272C80.8243 17.0272 82.1824 18.3859 82.1824 20.1712C82.1824 21.9785 80.824 23.3369 79.0166 23.3369Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M89.3208 25.5749V10.1999H86.7565V25.5749H89.3208Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M105.545 25.9904H102.982L102.985 26.0687C103.052 27.5892 103.87 28.5783 104.937 29.1838C106.001 29.7875 107.314 30.0119 108.389 30.0119C109.947 30.0119 111.32 29.5453 112.304 28.6969C113.289 27.8475 113.879 26.6193 113.879 25.1084V24.5092H113.901V14.7892H111.315V16.4506C110.524 15.2589 109.248 14.5717 107.606 14.5717C104.711 14.5717 102.441 17.0019 102.441 20.0189C102.441 23.0356 104.711 25.4879 107.606 25.4879C109.248 25.4879 110.524 24.8007 111.315 23.5909L111.293 25.1954C111.293 26.7752 109.968 27.8392 108.323 27.8392C107.572 27.8392 106.891 27.6726 106.392 27.3663C105.894 27.0609 105.58 26.6189 105.549 26.0613L105.545 25.9904ZM108.128 23.0977C106.363 23.0977 104.984 21.7165 104.984 20.0189C104.984 18.322 106.362 16.9619 108.128 16.9619C109.937 16.9619 111.293 18.2792 111.293 20.0189C111.293 21.7593 109.937 23.0977 108.128 23.0977Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M101.415 14.673L101.451 14.5724H98.6476L98.6251 14.6021C98.0604 15.346 97.1676 15.815 96.1565 15.815C95.1326 15.815 94.2498 15.346 93.6954 14.6026L93.6729 14.5724H90.8812L90.9172 14.6728C91.6891 16.8227 93.7038 18.2923 96.1565 18.2923C98.609 18.2923 100.637 16.8229 101.415 14.673Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M99.0679 25.7924H101.655L101.666 25.7305C101.725 25.3955 101.756 25.0488 101.756 24.6928C101.756 21.4976 99.3082 19.0715 96.1565 19.0715C93.0044 19.0715 90.5787 21.498 90.5787 24.6928C90.5787 25.0487 90.6093 25.3954 90.668 25.7304L90.6788 25.7924H93.2617L93.2306 25.6947C93.1313 25.3823 93.0777 25.0458 93.0777 24.6928C93.0777 22.9052 94.4164 21.527 96.1565 21.527C97.8758 21.527 99.257 22.9062 99.257 24.6928C99.257 25.0456 99.2018 25.3819 99.0999 25.6942L99.0679 25.7924Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default Catalog;
