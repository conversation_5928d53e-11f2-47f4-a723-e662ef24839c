import * as React from "react";
import { SVGProps } from "react";
const EasyTax = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={120}
    height={36}
    viewBox="0 0 120 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g opacity={0.84}>
      <path
        d="M24.875 19.5C24.875 23.6421 21.5171 27 17.375 27C13.2329 27 9.875 23.6421 9.875 19.5H3.875C3.875 26.9558 9.91916 33 17.375 33C24.8308 33 30.875 26.9558 30.875 19.5C30.875 12.0442 24.8308 6 17.375 6V12C21.5171 12 24.875 15.3579 24.875 19.5Z"
        fill="currentColor"
      />
      <path
        opacity={0.5}
        d="M8.375 3C8.375 7.14214 5.01714 10.5 0.875 10.5V16.5C8.33084 16.5 14.375 10.4558 14.375 3H8.375Z"
        fill="currentColor"
      />
    </g>
    <g opacity={0.84}>
      <path
        d="M38.375 10.3726H48.7025V13.2683H41.9188V16.1033H48.4595V18.9788H41.9188V21.8543H48.8645V24.7501H38.375V10.3726Z"
        fill="currentColor"
      />
      <path
        d="M49.8148 17.3993C50.2198 15.0301 52.0018 13.7746 54.9988 13.7746C58.4413 13.7746 60.1423 15.3541 60.1423 18.4928V21.5506C60.1423 22.3403 60.4663 22.4618 60.8308 22.4618H61.1953V24.7501L60.7093 24.7906C60.1423 24.8108 57.6718 25.1956 57.3073 23.1301C56.7403 24.2843 55.5253 24.9931 53.4801 24.9931C51.2931 24.9931 49.6326 23.8186 49.6326 21.9151C49.6326 19.9913 51.0703 19.1813 53.7231 18.6548L56.5986 18.0676C56.5986 16.5893 56.0923 15.9008 54.9786 15.9008C54.0471 15.9008 53.4801 16.4476 53.2978 17.5411L49.8148 17.3993ZM53.2168 21.7733C53.2168 22.4011 53.7231 22.8466 54.6748 22.8466C55.8493 22.8466 56.6593 21.9556 56.6593 20.1938V20.0926L55.1406 20.3761C53.9863 20.5786 53.2168 20.8216 53.2168 21.7733Z"
        fill="currentColor"
      />
      <path
        d="M68.4849 17.5613C68.3229 16.6096 67.5736 16.0426 66.7636 16.0426C65.9131 16.0426 65.3056 16.4881 65.3461 17.1968C65.3866 17.8853 66.1561 18.2093 67.1079 18.3713C70.5301 18.8776 71.9881 19.7483 71.9881 21.7531C71.9881 23.8996 70.0036 24.9931 67.0471 24.9931C63.7059 24.9931 61.6809 23.6363 61.4986 21.3278L65.0019 21.1861C65.1841 22.1378 65.8524 22.6643 66.9256 22.6643C67.6546 22.6643 68.4444 22.3808 68.4039 21.7126C68.3836 20.9228 67.5331 20.7203 66.5409 20.5381C63.2401 19.9306 61.7619 19.1206 61.7619 17.1968C61.7619 15.0301 63.5844 13.7543 66.8649 13.7543C69.7404 13.7543 71.6034 15.1111 71.9476 17.4398L68.4849 17.5613Z"
        fill="currentColor"
      />
      <path
        d="M71.7557 14.0176H74.9957L77.5067 21.1253L79.8962 14.0176H83.1362L78.9647 25.6613C78.4382 27.1396 77.4459 27.7876 75.7854 27.7876H73.6795V25.3171H75.0767C75.7652 25.3171 76.1095 25.1348 76.312 24.6286L76.5347 24.0818H75.6032L71.7557 14.0176Z"
        fill="currentColor"
      />
      <path
        d="M95.1394 10.3726V13.2683H90.8869V24.7501H87.3634V13.2683H83.1109V10.3726H95.1394Z"
        fill="currentColor"
      />
      <path
        d="M95.2223 17.3993C95.6273 15.0301 97.4093 13.7746 100.406 13.7746C103.849 13.7746 105.55 15.3541 105.55 18.4928V21.5506C105.55 22.3403 105.874 22.4618 106.238 22.4618H106.603V24.7501L106.117 24.7906C105.55 24.8108 103.079 25.1956 102.715 23.1301C102.148 24.2843 100.933 24.9931 98.8875 24.9931C96.7005 24.9931 95.04 23.8186 95.04 21.9151C95.04 19.9913 96.4778 19.1813 99.1305 18.6548L102.006 18.0676C102.006 16.5893 101.5 15.9008 100.386 15.9008C99.4545 15.9008 98.8875 16.4476 98.7053 17.5411L95.2223 17.3993ZM98.6243 21.7733C98.6243 22.4011 99.1305 22.8466 100.082 22.8466C101.257 22.8466 102.067 21.9556 102.067 20.1938V20.0926L100.548 20.3761C99.3938 20.5786 98.6243 20.8216 98.6243 21.7733Z"
        fill="currentColor"
      />
      <path
        d="M115.144 13.9973H118.911L115.124 19.3028L119.012 24.7501H115.246L113.14 21.4696L111.013 24.7501H107.247L111.135 19.3028L107.348 13.9973H111.094L113.14 17.1158L115.144 13.9973Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default EasyTax;
