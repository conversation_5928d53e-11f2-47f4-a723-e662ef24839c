import * as React from "react";
import { SVGProps } from "react";
const GPXOverlay = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={147}
    height={18}
    viewBox="0 0 3235 394"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M311 237.222q.814-.09 1-.993-56.491-1.488-113-.992v-77.411h198v39.698q-4.402 114.232-103 172.189-84.039 41.67-172 8.932Q5.263 323.425 0 194.546 7.725 68.598 122 14.418q100.831-36.964 191 20.841 26.72 18.826 46 45.156l-63 45.653q-52.62-63.015-133-43.171-87.444 33.342-83.5 126.536 10.252 69.26 74.5 97.756 74.577 25.469 131.5-28.285a322 322 0 0 0 15-18.856 176 176 0 0 0 10.5-22.826m211 18.856v-73.441q7.793.477 15-.992 72.603.652 62.5-70.464-8.963-27.25-37.5-32.254a192 192 0 0 1-11-1.985 2329 2329 0 0 0-68-.496V391.05h-79V3.005q81.501-.248 163 .496 42.492 3.798 76 29.774 29.448 27.926 36.5 67.982 4.473 36.13-4 71.456-22.665 63.666-89.5 77.907a233.4 233.4 0 0 1-64 5.458"
      fill="currentColor"
      opacity="0.5"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M750 3.005q43.985 64.466 88 129.018A8343 8343 0 0 0 926 3.005q45.255-.493 90 .992a258470 258470 0 0 1-268 386.557q-46.5.993-93 0l133-193.526A36133 36133 0 0 1 658 3.998q45.743-1.487 92-.993m610 0h85q45.915 128.536 94.5 256.05a58852 58852 0 0 0 94-255.058 1206 1206 0 0 1 84.5-.496 134605 134605 0 0 0-146.5 385.564c-10.61 1.311-21.44 1.973-32.5 1.985-11.06-.012-21.89-.674-32.5-1.985-48.85-128.708-97.68-257.394-146.5-386.06m626 0v73.44h-253V3.006zm199 0c79.23 4.863 120.9 46.546 125 125.048q.855 42.302-22.5 77.41-21.9 27.684-55.5 39.202c34.21 48.551 68.54 97.015 103 145.393a1137 1137 0 0 1-95 0c-30.69-44.016-61.36-88.015-92-131.995v-75.426c6.85.322 13.52-.009 20-.992q74.715-2.952 58.5-75.426c-5.97-13.48-16.14-22.247-30.5-26.3a140.5 140.5 0 0 0-19-2.48c-22.49-.99-45.16-1.322-68-.993V391.05h-78V3.005zm422 313.612c-.17 14.229 0 28.454.5 42.675q1.56-1.108 1.5-2.977c44.87-117.705 89.53-235.475 134-353.31q34.515-.495 69 .992a47374 47374 0 0 1 148 387.053c-27.67.331-55.34 0-83-.992-33.07-89.689-66.23-179.34-99.5-268.952-32.73 89.689-65.57 179.339-98.5 268.952-108.66.992-217.33 1.323-326 .992V26.824a3601 3601 0 0 0 78.5-24.811c.5 104.867.67 209.736.5 314.604zM2979 3.005a6964 6964 0 0 0 82 134.972 6899 6899 0 0 0 82-134.972c30.84-.329 61.5.002 92 .992a48552 48552 0 0 0-134.5 216.353c-.5 56.899-.67 113.799-.5 170.7h-78c.17-56.901 0-113.801-.5-170.7A42187 42187 0 0 1 2888 3.997c30.16-.99 60.5-1.32 91-.992m-993 157.798v73.441h-253v-73.441zM537 181.645q-7.207 1.47-15 .992v73.441a1385 1385 0 0 1-1-74.433z"
      fill="currentColor"
      opacity="0.5"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M956 295.776q-47.257-.494-94 .992-.814-.089-1-.992 47.746-1.98 95 0m1883 .992a1876 1876 0 0 0-122 0c-.54-.059-.88-.39-1-.992 41.33-1.323 82.67-1.323 124 0-.12.602-.46.933-1 .992"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M956 295.776c21.53 31.65 43.2 63.243 65 94.778-31.33.662-62.67.662-94 0a13667 13667 0 0 0-65-93.786q46.743-1.486 94-.992m1883 .992a3429 3429 0 0 1-61 93.29c-20.36-31.114-40.69-62.21-61-93.29 40.67-1.323 81.33-1.323 122 0"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1733 318.602h253v72.448h-253z"
      fill="currentColor"
      opacity="0.5"
    />
    <path
      d="M1342.5 198.489c0 85.786-70.36 156.011-158 156.011s-158-70.225-158-156.011c0-85.787 70.36-156.012 158-156.012s158 70.225 158 156.012Z"
      stroke="currentColor"
      opacity="0.5"
      strokeWidth={79}
    />
  </svg>
);
export default GPXOverlay;
