import * as React from "react";
import { SVGProps } from "react";
const Per<PERSON>rin = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={125}
    height={36}
    viewBox="0 0 125 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g opacity={0.84}>
      <path
        d="M23.85 8.09985H0.75L4.05 14.6998H23.85C27.4951 14.6998 30.45 17.6547 30.45 21.2998C30.45 24.9448 27.4951 27.8997 23.85 27.8997C29.3176 27.8997 33.75 23.4674 33.75 17.9998C33.75 12.5322 29.3176 8.09985 23.85 8.09985Z"
        fill="currentColor"
      />
      <path
        opacity={0.3}
        d="M16.3376 26.0756C15.2405 23.8814 16.8361 21.2998 19.2892 21.2998H23.8497C25.6723 21.2998 27.1497 22.7773 27.1497 24.5998C27.1497 26.4223 25.6723 27.8998 23.8497 27.8998H19.2892C18.0393 27.8998 16.8966 27.1936 16.3376 26.0756Z"
        fill="currentColor"
      />
      <path
        opacity={0.6}
        d="M23.8506 14.7002H7.35059L10.6506 21.3002H23.8506C25.6731 21.3002 27.1506 22.7776 27.1506 24.6001C27.1506 26.4227 25.6731 27.9001 23.8506 27.9001C27.4957 27.9001 30.4506 24.9452 30.4506 21.3002C30.4506 17.6551 27.4957 14.7002 23.8506 14.7002Z"
        fill="currentColor"
      />
    </g>
    <g opacity={0.84}>
      <path
        d="M108.135 13.0319C107.407 13.0319 106.875 12.8989 106.539 12.6329C106.217 12.3529 106.056 11.9189 106.056 11.3309C106.056 10.7289 106.217 10.2949 106.539 10.0289C106.875 9.74889 107.407 9.60889 108.135 9.60889C108.849 9.60889 109.374 9.74889 109.71 10.0289C110.046 10.2949 110.214 10.7289 110.214 11.3309C110.214 11.9189 110.046 12.3529 109.71 12.6329C109.374 12.8989 108.849 13.0319 108.135 13.0319Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M50.511 11.4359C49.713 11.0719 48.747 10.8899 47.613 10.8899H41.25V24.7499H44.862V20.1299H47.613C48.747 20.1299 49.713 19.9479 50.511 19.5839C51.309 19.2199 51.918 18.6949 52.338 18.0089C52.758 17.3229 52.968 16.4899 52.968 15.5099C52.968 14.5299 52.758 13.6969 52.338 13.0109C51.918 12.3249 51.309 11.7999 50.511 11.4359ZM48.747 16.9589C48.369 17.2669 47.802 17.4209 47.046 17.4209H44.862V13.5989H47.046C47.802 13.5989 48.369 13.7599 48.747 14.0819C49.139 14.3899 49.335 14.8659 49.335 15.5099C49.335 16.1539 49.139 16.6369 48.747 16.9589Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M56.1672 24.3089C57.0212 24.7429 58.0292 24.9599 59.1912 24.9599C59.9192 24.9599 60.6122 24.8549 61.2702 24.6449C61.9422 24.4349 62.5162 24.1269 62.9922 23.7209C63.4822 23.3009 63.8252 22.7899 64.0212 22.1879L61.1652 21.2429C61.0112 21.6209 60.7732 21.9149 60.4512 22.1249C60.1432 22.3349 59.7232 22.4399 59.1912 22.4399C58.7152 22.4399 58.3092 22.3489 57.9732 22.1669C57.6372 21.9709 57.3782 21.6629 57.1962 21.2429C57.0951 21.0018 57.0221 20.7218 56.9771 20.4029H64.1472C64.1752 20.2349 64.1962 20.0249 64.2102 19.7729C64.2242 19.5209 64.2312 19.2689 64.2312 19.0169C64.2312 18.0509 64.0282 17.1969 63.6222 16.4549C63.2162 15.6989 62.6212 15.1109 61.8372 14.6909C61.0672 14.2569 60.1292 14.0399 59.0232 14.0399C57.9452 14.0399 56.9862 14.2569 56.1462 14.6909C55.3202 15.1249 54.6762 15.7479 54.2142 16.5599C53.7522 17.3719 53.5212 18.3519 53.5212 19.4999C53.5212 20.6479 53.7522 21.6279 54.2142 22.4399C54.6762 23.2519 55.3272 23.8749 56.1672 24.3089ZM56.9978 18.4919C57.0356 18.2514 57.0877 18.0344 57.1542 17.8409C57.3222 17.3929 57.5602 17.0639 57.8682 16.8539C58.1902 16.6439 58.5752 16.5389 59.0232 16.5389C59.6532 16.5389 60.1152 16.7629 60.4092 17.2109C60.6322 17.5352 60.7744 17.9622 60.836 18.4919H56.9978Z"
        fill="currentColor"
      />
      <path
        d="M69.4834 18.1139C69.2734 18.4639 69.1684 18.9119 69.1684 19.4579V24.7499H65.5984V14.2499H68.7484L68.8954 16.5179C69.1614 15.7339 69.5534 15.1249 70.0714 14.6909C70.6034 14.2569 71.2754 14.0399 72.0874 14.0399C72.3534 14.0399 72.5774 14.0609 72.7594 14.1029C72.9554 14.1449 73.1094 14.2009 73.2214 14.2709L72.8644 17.2109C72.7244 17.1549 72.5424 17.1129 72.3184 17.0849C72.1084 17.0429 71.8494 17.0219 71.5414 17.0219C71.1214 17.0219 70.7294 17.1129 70.3654 17.2949C70.0014 17.4769 69.7074 17.7499 69.4834 18.1139Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M75.9687 24.3089C76.8227 24.7429 77.8307 24.9599 78.9927 24.9599C79.7207 24.9599 80.4137 24.8549 81.0717 24.6449C81.7437 24.4349 82.3177 24.1269 82.7937 23.7209C83.2837 23.3009 83.6267 22.7899 83.8227 22.1879L80.9667 21.2429C80.8127 21.6209 80.5747 21.9149 80.2527 22.1249C79.9447 22.3349 79.5247 22.4399 78.9927 22.4399C78.5167 22.4399 78.1107 22.3489 77.7747 22.1669C77.4387 21.9709 77.1797 21.6629 76.9977 21.2429C76.8966 21.0018 76.8236 20.7218 76.7787 20.4029H83.9487C83.9767 20.2349 83.9977 20.0249 84.0117 19.7729C84.0257 19.5209 84.0327 19.2689 84.0327 19.0169C84.0327 18.0509 83.8297 17.1969 83.4237 16.4549C83.0177 15.6989 82.4227 15.1109 81.6387 14.6909C80.8687 14.2569 79.9307 14.0399 78.8247 14.0399C77.7467 14.0399 76.7877 14.2569 75.9477 14.6909C75.1217 15.1249 74.4777 15.7479 74.0157 16.5599C73.5537 17.3719 73.3227 18.3519 73.3227 19.4999C73.3227 20.6479 73.5537 21.6279 74.0157 22.4399C74.4777 23.2519 75.1287 23.8749 75.9687 24.3089ZM76.7993 18.4919C76.8371 18.2514 76.8892 18.0344 76.9557 17.8409C77.1237 17.3929 77.3617 17.0639 77.6697 16.8539C77.9917 16.6439 78.3767 16.5389 78.8247 16.5389C79.4547 16.5389 79.9167 16.7629 80.2107 17.2109C80.4337 17.5352 80.576 17.9622 80.6375 18.4919H76.7993Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M90.3349 29.2649C89.4249 29.2649 88.5919 29.1529 87.8359 28.9289C87.0799 28.7189 86.4499 28.3409 85.9459 27.7949C85.4559 27.2629 85.1409 26.5139 85.0009 25.5479L88.4449 25.1069C88.5009 25.7229 88.6969 26.1919 89.0329 26.5139C89.3829 26.8499 89.8869 27.0179 90.5449 27.0179C91.1609 27.0179 91.6299 26.8219 91.9519 26.4299C92.2879 26.0379 92.4559 25.4849 92.4559 24.7709V22.7339C92.1479 23.2519 91.7209 23.6719 91.1749 23.9939C90.6429 24.3019 90.0129 24.4559 89.2849 24.4559C88.4029 24.4559 87.6329 24.2529 86.9749 23.8469C86.3309 23.4409 85.8339 22.8529 85.4839 22.0829C85.1339 21.2989 84.9589 20.3679 84.9589 19.2899C84.9589 18.1839 85.1479 17.2389 85.5259 16.4549C85.9039 15.6709 86.4149 15.0759 87.0589 14.6699C87.7169 14.2499 88.4589 14.0399 89.2849 14.0399C90.2089 14.0399 90.9579 14.2709 91.5319 14.7329C92.1059 15.1809 92.5119 15.7689 92.7499 16.4969L92.8339 14.2499H96.0469V23.9099C96.0469 25.2119 95.8159 26.2479 95.3539 27.0179C94.8919 27.8019 94.2339 28.3689 93.3799 28.7189C92.5259 29.0829 91.5109 29.2649 90.3349 29.2649ZM90.5239 21.8519C91.1119 21.8519 91.5809 21.6349 91.9309 21.2009C92.2809 20.7669 92.4559 20.1159 92.4559 19.2479C92.4559 18.4079 92.2879 17.7709 91.9519 17.3369C91.6159 16.8889 91.1399 16.6649 90.5239 16.6649C90.1179 16.6649 89.7679 16.7629 89.4739 16.9589C89.1799 17.1549 88.9489 17.4419 88.7809 17.8199C88.6269 18.1979 88.5499 18.6739 88.5499 19.2479C88.5499 20.1439 88.7249 20.8019 89.0749 21.2219C89.4249 21.6419 89.9079 21.8519 90.5239 21.8519Z"
        fill="currentColor"
      />
      <path
        d="M101.253 16.5179L101.106 14.2499H97.9564V24.7499H101.526V19.4579C101.526 18.9119 101.631 18.4639 101.841 18.1139C102.065 17.7499 102.359 17.4769 102.723 17.2949C103.087 17.1129 103.479 17.0219 103.899 17.0219C104.207 17.0219 104.466 17.0429 104.676 17.0849C104.9 17.1129 105.082 17.1549 105.222 17.2109L105.579 14.2709C105.467 14.2009 105.313 14.1449 105.117 14.1029C104.935 14.0609 104.711 14.0399 104.445 14.0399C103.633 14.0399 102.961 14.2569 102.429 14.6909C101.911 15.1249 101.519 15.7339 101.253 16.5179Z"
        fill="currentColor"
      />
      <path
        d="M120.609 14.4599C120.077 14.1799 119.447 14.0399 118.719 14.0399C117.907 14.0399 117.193 14.2289 116.577 14.6069C115.961 14.9709 115.478 15.5239 115.128 16.2659L115.122 16.2797L115.023 14.2499H111.831V24.7499H115.401V19.2059C115.401 18.6319 115.485 18.1629 115.653 17.7989C115.821 17.4349 116.052 17.1759 116.346 17.0219C116.64 16.8539 116.955 16.7699 117.291 16.7699C117.739 16.7699 118.096 16.9099 118.362 17.1899C118.642 17.4699 118.782 17.9879 118.782 18.7439V23.9729C118.782 24.6169 118.915 25.1559 119.181 25.5899C119.447 26.0239 119.811 26.3459 120.273 26.5559C120.749 26.7799 121.288 26.8919 121.89 26.8919C122.38 26.8919 122.765 26.8499 123.045 26.7659C123.339 26.6959 123.661 26.5769 124.011 26.4089L124.263 24.2669C124.095 24.3509 123.934 24.4069 123.78 24.4349C123.64 24.4629 123.465 24.4769 123.255 24.4769C123.003 24.4769 122.786 24.4069 122.604 24.2669C122.436 24.1129 122.352 23.8609 122.352 23.5109V17.7569C122.352 16.9309 122.198 16.2449 121.89 15.6989C121.582 15.1529 121.155 14.7399 120.609 14.4599Z"
        fill="currentColor"
      />
      <path
        d="M109.92 14.2499V24.7499H106.35V14.2499H109.92Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default Peregrin;
