import * as React from "react";
import { SVGProps } from "react";
const CoreOS = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={110}
    height={36}
    viewBox="0 0 110 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g opacity={0.84}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.7964 3.74951C9.38489 3.74951 7.15654 5.03605 5.95076 7.12451L1.62064 14.6245C0.414867 16.713 0.414865 19.2861 1.62064 21.3745L5.95076 28.8745C7.15654 30.963 9.38489 32.2495 11.7964 32.2495H20.4567C22.8682 32.2495 25.0966 30.963 26.3024 28.8745L30.6325 21.3745C31.8383 19.2861 31.8383 16.713 30.6325 14.6245L26.3024 7.12451C25.0966 5.03606 22.8682 3.74951 20.4567 3.74951H11.7964ZM20.4567 8.24951L15.044 8.24951C14.178 8.24951 13.6369 9.18693 14.0705 9.93655C15.4085 12.2494 16.7497 14.5605 18.0857 16.8745C18.4876 17.5707 18.4876 18.4284 18.0857 19.1245C16.7497 21.4385 15.4085 23.7496 14.0705 26.0625C13.6369 26.8121 14.178 27.7495 15.044 27.7495H20.4567C21.2605 27.7495 22.0033 27.3207 22.4052 26.6245L26.7354 19.1245C27.1373 18.4284 27.1373 17.5707 26.7354 16.8745L22.4052 9.37451C22.0033 8.67836 21.2605 8.24951 20.4567 8.24951Z"
        fill="currentColor"
      />
    </g>
    <g opacity={0.84}>
      <path
        d="M50.6123 15.3945H47.2102C46.8255 14.0378 45.8535 13.3088 44.4765 13.3088C42.3908 13.3088 41.196 15.0503 41.196 17.8853C41.196 20.7405 42.3705 22.4213 44.4765 22.4213C45.7725 22.4213 46.785 21.7328 47.1698 20.4368H50.6123C49.9035 23.4743 47.4532 25.074 44.517 25.074C40.4062 25.074 37.875 22.3403 37.875 17.8853C37.875 13.4303 40.4063 10.656 44.5575 10.656C47.514 10.656 50.025 12.2558 50.6123 15.3945Z"
        fill="currentColor"
      />
      <path
        d="M56.7331 25.074C53.4324 25.074 51.3871 22.887 51.3871 19.3028C51.3871 15.597 53.4931 13.5113 56.7331 13.5113C60.0339 13.5113 62.0994 15.7388 62.0994 19.3028C62.0994 22.9883 59.9731 25.074 56.7331 25.074ZM56.7331 22.563C58.1709 22.563 58.9201 21.429 58.9201 19.3028C58.9201 17.1968 58.1506 16.0223 56.7331 16.0223C55.3156 16.0223 54.5664 17.1765 54.5664 19.3028C54.5664 21.4088 55.3359 22.563 56.7331 22.563Z"
        fill="currentColor"
      />
      <path
        d="M63.3329 24.75V13.8555H66.4311V15.3743C67.1196 14.0783 68.1726 13.5113 69.2864 13.5113C69.7319 13.5113 70.1369 13.6328 70.3596 13.8555V16.4678C69.9951 16.3868 69.6104 16.3463 69.1244 16.3463C67.2614 16.3463 66.4311 17.3993 66.4311 19.1205V24.75H63.3329Z"
        fill="currentColor"
      />
      <path
        d="M81.263 21.1658C80.696 23.6768 78.671 25.074 75.998 25.074C72.6162 25.074 70.5305 22.887 70.5305 19.3028C70.5305 15.597 72.6365 13.5113 75.8765 13.5113C79.1367 13.5113 81.182 15.678 81.182 19.242V19.9305H73.6895C73.8515 21.672 74.6615 22.6035 75.998 22.6035C77.0105 22.6035 77.699 22.158 78.023 21.1658H81.263ZM75.8765 15.9818C74.7223 15.9818 73.9932 16.731 73.7502 18.1485H77.9825C77.7395 16.731 77.0105 15.9818 75.8765 15.9818Z"
        fill="currentColor"
      />
      <path
        d="M89.1183 25.074C84.8455 25.074 82.213 22.32 82.213 17.8853C82.213 13.4303 84.8455 10.656 89.1183 10.656C93.3708 10.656 96.0033 13.4303 96.0033 17.8853C96.0033 22.32 93.3708 25.074 89.1183 25.074ZM89.1183 22.4213C91.366 22.4213 92.6823 20.7203 92.6823 17.8853C92.6823 15.0503 91.366 13.3088 89.1183 13.3088C86.8503 13.3088 85.534 15.03 85.534 17.8853C85.534 20.7203 86.8503 22.4213 89.1183 22.4213Z"
        fill="currentColor"
      />
      <path
        d="M100.474 14.5238C100.474 17.46 108.574 14.9693 108.574 20.5988C108.574 23.4338 106.225 25.074 103.005 25.074C99.7452 25.074 97.4165 23.5755 96.9305 20.4368H100.353C100.616 21.7935 101.568 22.5428 103.026 22.5428C104.484 22.5428 105.253 21.9555 105.253 21.0038C105.253 17.9258 97.133 20.2748 97.133 14.8275C97.133 12.519 99.0567 10.656 102.438 10.656C105.294 10.656 107.764 12.0128 108.189 15.192H104.727C104.443 13.8353 103.653 13.1873 102.317 13.1873C101.183 13.1873 100.474 13.7138 100.474 14.5238Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default CoreOS;
