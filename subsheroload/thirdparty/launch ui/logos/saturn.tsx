const Saturn = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M7.5 18.75L3.75 21L21 3.75L18.75 7.5L7.5 18.75Z"
      fill="currentColor"
    />
    <path
      d="M16.5 5.25L20.25 3L3 20.25L5.25 16.5L16.5 5.25Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.3833 6.86669C15.204 5.85869 13.6731 5.25 12 5.25C8.27208 5.25 5.25 8.27208 5.25 12C5.25 13.6731 5.85869 15.204 6.86669 16.3833L16.3833 6.86669Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 18.75C12 18.75 12 18.75 12 18.75C15.728 18.75 18.75 15.728 18.75 12C18.75 12 18.75 12 18.75 12L12 18.75ZM18.6125 10.6376C18.5308 10.2393 18.4141 9.85373 18.2657 9.48438L9.48438 18.2657C9.85373 18.4141 10.2393 18.5308 10.6376 18.6125L18.6125 10.6376Z"
      fill="currentColor"
    />
  </svg>
);
export default Saturn;
