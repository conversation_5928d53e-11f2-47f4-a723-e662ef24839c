const Luna = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={21} cy={11} r={4} fill="#D9D9D9" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.02939 7.02936L24.9706 23.9706C24.6572 24.3232 24.3232 24.6571 23.9706 24.9706L7.02939 8.02936C7.34287 7.6768 7.67683 7.34284 8.02939 7.02936ZM6.14761 9.14758L22.8524 25.8524C22.4553 26.1291 22.0406 26.3825 21.6104 26.6104L5.3896 10.3896C5.61754 9.95938 5.87087 9.54472 6.14761 9.14758ZM4.76768 11.7676L20.2324 27.2323C19.7351 27.4198 19.2222 27.5752 18.696 27.696L4.30405 13.304C4.42484 12.7778 4.58022 12.2649 4.76768 11.7676ZM4.038 15.038L16.962 27.962C16.6447 27.9872 16.3238 28 16 28C15.648 28 15.2995 27.9848 14.9552 27.9551L4.04486 17.0448C4.01516 16.7005 4 16.352 4 16C4 15.6762 4.01283 15.3553 4.038 15.038ZM12.4735 27.4735C8.68209 26.3095 5.69048 23.3179 4.52654 19.5265L12.4735 27.4735Z"
      fill="currentColor"
    />
  </svg>
);
export default Luna;
