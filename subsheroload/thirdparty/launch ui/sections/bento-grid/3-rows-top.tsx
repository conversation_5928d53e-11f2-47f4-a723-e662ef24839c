import {
  Tile,
  TileVisual,
  TileTitle,
  TileDescription,
  TileContent,
  TileLink,
} from "../../ui/tile";
import { Section } from "../../ui/section";
import GlobeIllustration from "../../illustrations/globe";
import ChatIllustration from "../../illustrations/chat";
import MockupMobileIllustration from "../../illustrations/mockup-mobile";
import PipelineIllustration from "../../illustrations/pipeline";
import CodeEditorIllustration from "../../illustrations/code-editor";
import RadarSmallIllustration from "../../illustrations/radar-small";
import { ReactNode } from "react";

interface TileProps {
  title: string;
  description: ReactNode;
  visual: ReactNode;
  size?: string;
  icon?: ReactNode;
}

interface BentoGridProps {
  title?: string;
  description?: string;
  tiles?: TileProps[] | false;
  className?: string;
}

export default function BentoGrid({
  title = "It's all about design quality",
  description = "In a world where everybody can vibe-code a new thing overnight, the key is to stand out. Generic-looking shadcn/ui blocks won't help you to get noticed. With Launch UI, you can make sure that your design is unique and memorable.",
  tiles = [
    {
      title: "Design-first",
      description: (
        <p>
          Each block and component is carfully crafted—not generated by AI—to
          give a unique look and feel AI would never come up with.
        </p>
      ),
      visual: (
        <div className="min-h-[300px] w-full py-12">
          <MockupMobileIllustration />
        </div>
      ),
      size: "col-span-12 md:col-span-6 lg:col-span-5",
    },
    {
      title: "Made for fast development",
      description: (
        <p className="max-w-[520px]">
          With lightweight code, modern tooling and best practice, Launch UI is
          as fast in the browser as it is to build with.
        </p>
      ),
      visual: (
        <div className="min-h-[160px] w-full grow items-center self-center">
          <PipelineIllustration />
        </div>
      ),
      size: "col-span-12 md:col-span-6 lg:col-span-7",
    },
    {
      title: "The code is yours",
      description: (
        <p className="max-w-[460px]">
          With Launch UI, the code is yours forever. Never bother about
          subscriptions and lock-ins.
        </p>
      ),
      visual: (
        <div className="min-h-[240px] w-full grow items-center self-center px-4 lg:px-12">
          <CodeEditorIllustration />
        </div>
      ),
      size: "col-span-12 md:col-span-6 lg:col-span-7",
    },
    {
      title: "Top-level performance",
      description:
        "Made for static sites while avoiding heavy assets, your website will feel snappy and load instantly.",
      visual: (
        <div className="-mb-[96px] sm:-mb-[186px] md:-mx-32">
          <GlobeIllustration className="[&_svg]:h-[100%] [&_svg]:w-[100%]" />
        </div>
      ),
      size: "col-span-12 md:col-span-6 lg:col-span-5",
    },
    {
      title: "Data-agnostic",
      description: (
        <>
          <p>
            All the data is separate from components so you can edit it in
            seconds or make it dynamic.
          </p>
          <p>Easily connect to a CMS of your choice.</p>
        </>
      ),
      visual: (
        <div className="w-full sm:p-4 md:p-8">
          <ChatIllustration />
        </div>
      ),
      size: "col-span-12 md:col-span-6 lg:col-span-5",
    },
    {
      title: "Fits right into your stack",
      description: (
        <>
          <p className="max-w-[460px]">
            Build your website and product with the same technologies and
            frameworks. Forget about multiple codebases and unnecessary APIs.
          </p>
          <p>No extra dependencies, no extra maintenance.</p>
        </>
      ),
      visual: (
        <div className="mt-12 -mb-48 flex w-full grow items-center justify-center self-center">
          <RadarSmallIllustration className="max-w-[480px]" />
        </div>
      ),
      size: "col-span-12 md:col-span-6 lg:col-span-7",
    },
  ],
  className,
}: BentoGridProps) {
  return (
    <Section className={className}>
      <div className="max-w-container mx-auto flex flex-col items-center gap-6 sm:gap-12">
        <h2 className="text-center text-3xl font-semibold text-balance sm:text-5xl">
          {title}
        </h2>
        <p className="text-md text-muted-foreground max-w-[840px] text-center font-medium text-balance sm:text-xl">
          {description}
        </p>
        {tiles !== false && tiles.length > 0 && (
          <div className="grid grid-cols-12 gap-4">
            {tiles.map((tile, index) => (
              <Tile key={index} className={tile.size}>
                <TileLink />
                <TileContent>
                  {tile.icon && tile.icon}
                  <TileTitle>{tile.title}</TileTitle>
                  <TileDescription>{tile.description}</TileDescription>
                </TileContent>
                <TileVisual>{tile.visual}</TileVisual>
              </Tile>
            ))}
          </div>
        )}
      </div>
    </Section>
  );
}
